<?php

// This is the main file to run the game.
// Each player is now controlled by a unique AI model from your list.
// Make sure to have 'game_rules.json' and 'game_models.txt' in the same directory.

require_once 'vendor/autoload.php'; // For Composer dependencies

use ArdaGnsrn\Ollama\Ollama;

class Player {
    public $id;
    public $name;
    public $role;
    public $model; // Each player has its own AI model
    public $isAlive = true;
    public $privateHistory = [];
    public $votes = 0;
    public $protected = false;
    public $hasSelfProtected = false; // For Doctor's ability

    public function __construct($id, $name, $role, $model) {
        $this->id = $id;
        $this->name = $name;
        $this->role = $role;
        $this->model = $model;
    }

    public function resetForNewRound() {
        $this->votes = 0;
        $this->protected = false;
    }
}

class Game {
    private $players = [];
    private $round = 0;
    private $ollama;
    private $gameRules;
    private $gameModels;
    private $publicHistory = [];

    public function __construct(Ollama $ollama, array $gameRules, array $gameModels) {
        $this->ollama = $ollama;
        $this->gameRules = $gameRules;
        $this->gameModels = $gameModels;
        $this->initializePlayers();
    }

    private function initializePlayers() {
        $playerNames = ["Chuck", "Sarah", "Casey", "Morgan", "Jeff", "Lester", "Devon", "Ellie", "Finch", "Reese", "Carter", "Fusco", "Root", "Shaw", "Elias", "Greer"];
        shuffle($playerNames);
        
        $roles = $this->gameRules['roles'];
        $models = $this->gameModels;
        shuffle($models); // Randomize model assignments

        for ($i = 0; $i < count($roles); $i++) {
            $this->players[] = new Player($i + 1, $playerNames[$i], $roles[$i], trim($models[$i]));
        }

        echo "Players have been assigned their roles and AI models.\n";
        echo "-----------------------------------------------------\n";
        foreach ($this->players as $player) {
            echo "Player: {$player->name} | Role: {$player->role} | AI Model: {$player->model}\n";
        }
        echo "-----------------------------------------------------\n";
    }

    public function runGame() {
        while (!$this->isGameOver()) {
            $this->round++;
            echo "\n--- Round " . $this->round . " ---\n";
            $this->publicHistory[] = ["role" => "system", "content" => "Round " . $this->round];

            foreach ($this->players as $player) {
                $player->resetForNewRound();
            }

            $this->nightPhase();
            if ($this->isGameOver()) break;

            $this->dayPhase();
            if ($this->isGameOver()) break;

            $this->votingPhase();
            if ($this->isGameOver()) break;
        }
    }

    private function nightPhase() {
        echo "\nNight Phase...\n";
        $this->publicHistory[] = ["role" => "system", "content" => "It is now night. Everyone goes to sleep."];
        
        $vampireTargets = [];
        $doctorTarget = null;

        // Actions
        foreach ($this->getLivingPlayers() as $player) {
            switch ($player->role) {
                case 'Vampire':
                    $target = $this->promptPlayerAction($player, "choose a target to attack");
                    if($target) $vampireTargets[] = $target;
                    break;
                case 'Doctor':
                    $doctorTarget = $this->promptPlayerAction($player, "choose a player to protect");
                    break;
                case 'Observer':
                    $observedId = $this->promptPlayerAction($player, "choose a player to observe");
                    if ($observedId) {
                        $observedPlayer = $this->players[$observedId - 1];
                        $isVampire = ($observedPlayer->role === 'Vampire' || $observedPlayer->role === 'Musketeer');
                        $message = "You observed {$observedPlayer->name}. The response is: " . ($isVampire ? "Yes" : "No") . ".";
                        echo "MODERATOR INFO: {$player->name} (Observer) received info: {$message}\n";
                        $player->privateHistory[] = ['role' => 'assistant', 'content' => $message];
                    }
                    break;
            }
        }
        
        // Vampire attack resolution
        $finalVampireTarget = null;
        if (!empty($vampireTargets)) {
             $finalVampireTarget = $vampireTargets[array_rand($vampireTargets)];
        }

        // Night resolution
        if ($finalVampireTarget) {
            $targetPlayer = $this->players[$finalVampireTarget - 1];
            if ($doctorTarget && $doctorTarget == $finalVampireTarget) {
                $info = "No one got killed during the night.";
                echo $info . "\n";
                $this->publicHistory[] = ["role" => "system", "content" => $info];
            } else {
                $targetPlayer->isAlive = false;
                $info = "A player has been killed during the night. Their role is not revealed.";
                echo "{$targetPlayer->name} was killed.\n";
                $this->publicHistory[] = ["role" => "system", "content" => $info];
            }
        } else {
             $info = "No one got killed during the night.";
             echo $info . "\n";
             $this->publicHistory[] = ["role" => "system", "content" => $info];
        }
    }

    private function dayPhase() {
        echo "\nDay Phase...\n";
        $this->publicHistory[] = ["role" => "system", "content" => "It is now day. The town wakes up."];
        
        $livingPlayerNames = array_map(fn($p) => $p->name, $this->getLivingPlayers());
        echo "Living Players: " . implode(", ", $livingPlayerNames) . "\n";
        $this->publicHistory[] = ["role" => "system", "content" => "Living players: " . implode(", ", $livingPlayerNames)];

        echo "\nTown Discussion:\n";
        $discussionOrder = $this->getLivingPlayers();
        shuffle($discussionOrder);

        foreach ($discussionOrder as $player) {
            $prompt = $this->gameRules['prompts']['day_discussion'];
            $messages = $this->buildMessageContext($player, $prompt);

            $response = $this->ollama->chat(['model' => $player->model, 'messages' => $messages]);
            $statement = "{$player->name} ({$player->model}): {$response['message']['content']}";
            echo $statement . "\n";
            $this->publicHistory[] = ['role' => 'user', 'content' => "{$player->name}: {$response['message']['content']}"];
        }
    }

    private function votingPhase() {
        echo "\nVoting Phase...\n";
        $this->publicHistory[] = ["role" => "system", "content" => "It's time to vote. Accuse a player or say 'Pass'."];
        
        foreach ($this->getLivingPlayers() as $player) {
           $vote = $this->promptPlayerAction($player, "vote for a player to eliminate");
            if ($vote) {
                $this->players[$vote - 1]->votes++;
                $info = "{$player->name} voted for {$this->players[$vote-1]->name}.";
                echo $info ."\n";
            } else {
                 echo "{$player->name} passed the vote.\n";
            }
        }

        $maxVotes = 0;
        foreach ($this->getLivingPlayers() as $player) {
            if ($player->votes > $maxVotes) {
                $maxVotes = $player->votes;
            }
        }
        
        if ($maxVotes > 0) {
            $playersWithMaxVotes = array_filter($this->getLivingPlayers(), fn($p) => $p->votes == $maxVotes);

            if (count($playersWithMaxVotes) == 1) {
                $eliminatedPlayer = current($playersWithMaxVotes);
                $eliminatedPlayer->isAlive = false;
                $info = "{$eliminatedPlayer->name} has been voted out. Their role is not revealed.";
                echo $info . "\n";
                $this->publicHistory[] = ['role' => 'system', 'content' => $info];

                if($eliminatedPlayer->role === 'Clown'){
                     $this->announceWinner("The Clown");
                     exit;
                }
                if ($eliminatedPlayer->role === 'Musketeer') {
                    $this->handleMusketeerFinalAct($eliminatedPlayer);
                }

            } else {
                $info = "The vote is a tie. No one is eliminated.";
                echo $info . "\n";
                $this->publicHistory[] = ['role' => 'system', 'content' => $info];
            }
        } else {
             $info = "Everyone passed. No one is eliminated.";
            echo $info . "\n";
            $this->publicHistory[] = ['role' => 'system', 'content' => $info];
        }
    }
    
    private function handleMusketeerFinalAct($musketeer) {
        echo "The Musketeer, {$musketeer->name}, gets a final shot!\n";
        $targetId = $this->promptPlayerAction($musketeer, "choose one player to eliminate as your final act");
        if ($targetId && $this->players[$targetId - 1]->isAlive) {
            $shotPlayer = $this->players[$targetId - 1];
            $shotPlayer->isAlive = false;
            $info = "As a final act, {$musketeer->name} shot {$shotPlayer->name}! They have been eliminated.";
            echo $info . "\n";
            $this->publicHistory[] = ['role' => 'system', 'content' => $info];
        }
    }

    private function promptPlayerAction($player, $action) {
        $livingPlayersList = array_map(fn($p) => "{$p->id}:{$p->name}", array_filter($this->getLivingPlayers(), fn($pl) => $pl->id !== $player->id));
        
        $prompt = "You are {$player->name}, a {$player->role}. The living players you can target are: " . implode(", ", $livingPlayersList) . ". Please {$action}. Respond with the player ID only (e.g., '3').";
        if ($player->role === 'Doctor' && $player->hasSelfProtected) {
            $prompt .= " You have already protected yourself once, so you cannot choose yourself again.";
        }
        if (str_contains($action, 'vote')) {
            $prompt .= " You cannot vote for yourself. You can also respond with 'Pass'.";
        }

        $messages = $this->buildMessageContext($player, $prompt);

        $response = $this->ollama->chat(['model' => $player->model, 'messages' => $messages]);
        
        if (stripos($response['message']['content'], 'pass') !== false && str_contains($action, 'vote')) {
            return null;
        }

        preg_match('/\d+/', $response['message']['content'], $matches);
        $targetId = isset($matches[0]) ? (int)$matches[0] : null;

        $livingPlayerIds = array_map(fn($p) => $p->id, $this->getLivingPlayers());
        if ($targetId && in_array($targetId, $livingPlayerIds) && $targetId != $player->id) {
            if ($player->role === 'Doctor' && $targetId == $player->id) {
                if ($player->hasSelfProtected) {
                     return $this->getRandomTarget($player->id);
                }
                $player->hasSelfProtected = true;
            }
            $player->privateHistory[] = ['role' => 'user', 'content' => "I decided to {$action} player {$targetId}."];
            return $targetId;
        }
        
        echo "MODERATOR: {$player->name} made an invalid choice. A random target will be selected.\n";
        return $this->getRandomTarget($player->id);
    }
    
    private function getRandomTarget($playerIdToExclude) {
         $validTargets = array_filter($this->getLivingPlayers(), fn($p) => $p->id !== $playerIdToExclude);
        if(empty($validTargets)) return null;
        return $validTargets[array_rand($validTargets)]->id;
    }

    private function buildMessageContext($player, $prompt) {
         return array_merge(
            [["role" => "system", "content" => $this->gameRules['game_system_prompt']]],
            [["role" => "system", "content" => "Your name is {$player->name} and your role is {$player->role}. Here is your role description: " . $this->gameRules['role_descriptions'][$player->role]]],
            $this->publicHistory,
            $player->privateHistory,
            [['role' => 'user', 'content' => $prompt]]
        );
    }

    private function isGameOver() {
        $livingPlayers = $this->getLivingPlayers();
        $vampireCount = count(array_filter($livingPlayers, fn($p) => $p->role === 'Vampire'));
        $villagerCount = count($livingPlayers) - $vampireCount;

        if ($vampireCount === 0) {
            $this->announceWinner("The Peasants");
            return true;
        }
        if ($vampireCount >= $villagerCount) {
            $this->announceWinner("The Vampires");
            return true;
        }

        return false;
    }
    
    private function getLivingPlayers() {
        return array_filter($this->players, fn($p) => $p->isAlive);
    }

    private function announceWinner($winner) {
        echo "\n--- Game Over! ---\n";
        echo "The winner is: " . $winner . "!\n";
    }
}

// 1. Load Game Configuration
$gameRules = json_decode(file_get_contents("game_rules.json"), true);
$models = explode(",", file_get_contents("game_models.txt"));

// Ensure there are enough models for the number of roles
if (count($models) < count($gameRules['roles'])) {
    die("Error: Not enough models in game_models.txt. Need at least " . count($gameRules['roles']) . " models.");
}

// 2. Initialize Ollama Client
$ollama = Ollama::client('http://localhost:11434');

// 3. Instantiate and Run the Game
$game = new Game($ollama, $gameRules, $models);
$game->runGame();

